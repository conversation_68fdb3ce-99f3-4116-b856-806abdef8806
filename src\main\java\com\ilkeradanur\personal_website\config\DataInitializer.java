package com.ilkeradanur.personal_website.config;

import com.ilkeradanur.personal_website.entity.User;
import com.ilkeradanur.personal_website.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public void run(String... args) {
        try {
            // Admin kullanıcısı var mı kontrol et
            long userCount = userRepository.count();
            System.out.println("DEBUG: Toplam kullanıcı sayısı: " + userCount);

            // Tüm kullanıcıları listele
            userRepository.findAll().forEach(user -> {
                if (user != null) {
                    System.out.println("DEBUG: Mevcut kullanıcı - ID: " + user.getId() +
                                     ", Username: '" + user.getUsername() + "'" +
                                     ", Password: '" + user.getPassword() + "'" +
                                     ", Role: '" + user.getRole() + "'" +
                                     ", Enabled: " + user.isEnabled());
                } else {
                    System.out.println("DEBUG: NULL kullanıcı bulundu!");
                }
            });

            // Admin kullanıcısının var olup olmadığını kontrol et
            boolean adminExists = userRepository.findByUsername("admin").isPresent();
            System.out.println("DEBUG: Admin kullanıcısı mevcut mu? " + adminExists);

            if (!adminExists) {
                // Önce tüm geçerli kullanıcıları temizle
                List<User> allUsers = userRepository.findAll();
                for (User user : allUsers) {
                    if (user != null) {
                        userRepository.delete(user);
                    }
                }
                System.out.println("DEBUG: Tüm kullanıcılar temizlendi.");

                // Admin kullanıcısı oluştur
                User adminUser = new User();
                adminUser.setUsername("admin");
                adminUser.setPassword("admin"); // Şifreleme olmadığı için düz metin şifre
                adminUser.setRole("ROLE_ADMIN");
                adminUser.setEnabled(true);
                User savedUser = userRepository.save(adminUser);
                System.out.println("DEBUG: Varsayılan admin kullanıcısı oluşturuldu. ID: " + savedUser.getId());
            } else {
                System.out.println("Admin kullanıcısı zaten mevcut.");
            }
        } catch (Exception e) {
            System.err.println("Veritabanı başlatma hatası: " + e.getMessage());
            e.printStackTrace();
        }
    }
}