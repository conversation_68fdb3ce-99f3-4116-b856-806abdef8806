-- Users tablosu
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- About tablosu
CREATE TABLE IF NOT EXISTS about (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    content TEXT,
    profile_image_path VARCHAR(255),
    resume_path VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Skills tablosu
CREATE TABLE IF NOT EXISTS skills (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    description VARCHAR(255),
    proficiency INTEGER,
    icon VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    certification_name VARCHAR(255),
    certification_url VARCHAR(255),
    issuer VARCHAR(255),
    issue_date VARCHAR(255),
    expiry_date VARCHAR(255)
);

-- Projects tablosu
CREATE TABLE IF NOT EXISTS projects (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(255),
    description VARCHAR(1000),
    technologies VARCHAR(255),
    status VARCHAR(255) NOT NULL CHECK (status IN ('IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED')),
    start_date DATE,
    end_date DATE,
    github_link VARCHAR(255),
    demo_link VARCHAR(255)
);

-- Timeline tablosu
CREATE TABLE IF NOT EXISTS timeline (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN DEFAULT false,
    icon_class VARCHAR(255),
    category VARCHAR(255)
);

-- Blog Posts tablosu
CREATE TABLE IF NOT EXISTS blog_posts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255),
    content TEXT NOT NULL,
    summary VARCHAR(1000) NOT NULL,
    image_url VARCHAR(255),
    published BOOLEAN NOT NULL DEFAULT false,
    publish_date TIMESTAMP NOT NULL,
    last_modified_date TIMESTAMP
);

-- Blog Post Tags tablosu
CREATE TABLE IF NOT EXISTS blog_post_tags (
    post_id BIGINT NOT NULL,
    tag VARCHAR(255),
    FOREIGN KEY (post_id) REFERENCES blog_posts(id)
);

-- Messages tablosu
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message VARCHAR(2000),
    read BOOLEAN DEFAULT false,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
); 