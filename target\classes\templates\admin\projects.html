<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${title}"><PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><PERSON><PERSON>ler <PERSON>ö<PERSON>im<PERSON></h1>
                    <a href="/admin/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
                    </a>
                </div>

                <!-- Bildir<PERSON>ler -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Proje Ekleme Formu -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Yeni Proje Ekle</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/admin/projects}" method="post" th:object="${project}">
                            <input type="hidden" th:field="*{id}" />
                            
                            <div class="mb-3">
                                <label for="title" class="form-label">Proje Başlığı</label>
                                <input type="text" class="form-control" id="title" th:field="*{title}" required>
                            </div>

                            <div class="mb-3">
                                <label for="category" class="form-label">Kategori</label>
                                <input type="text" class="form-control" id="category" th:field="*{category}" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Açıklama</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3" required></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="technologies" class="form-label">Kullanılan Teknolojiler</label>
                                <input type="text" class="form-control" id="technologies" th:field="*{technologies}" required>
                                <small class="form-text text-muted">Virgülle ayırarak yazın</small>
                            </div>

                            <div class="mb-3">
                                <label for="githubLink" class="form-label">GitHub Linki</label>
                                <input type="url" class="form-control" id="githubLink" th:field="*{githubLink}">
                            </div>

                            <div class="mb-3">
                                <label for="demoLink" class="form-label">Demo Linki</label>
                                <input type="url" class="form-control" id="demoLink" th:field="*{demoLink}">
                            </div>

                            <div class="mb-3">
                                <label for="displayOrder" class="form-label">Görüntüleme Sırası</label>
                                <input type="number" class="form-control" id="displayOrder" th:field="*{displayOrder}">
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="isActive" th:field="*{isActive}">
                                    <label class="form-check-label" for="isActive">Aktif</label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="status" class="form-label">Durum</label>
                                <select class="form-select" id="status" th:field="*{status}" required>
                                    <option value="ACTIVE">Aktif</option>
                                    <option value="COMPLETED">Tamamlandı</option>
                                    <option value="ON_HOLD">Beklemede</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary">Kaydet</button>
                        </form>
                    </div>
                </div>

                <!-- Projeler Listesi -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Mevcut Projeler</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Başlık</th>
                                        <th>Kategori</th>
                                        <th>Teknolojiler</th>
                                        <th>Durum</th>
                                        <th>Sıra</th>
                                        <th>Aktif</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="project : ${projects}">
                                        <td th:text="${project.id}"></td>
                                        <td>
                                            <strong th:text="${project.title}"></strong>
                                            <div class="small text-muted" th:text="${#strings.abbreviate(project.description, 100)}"></div>
                                        </td>
                                        <td th:text="${project.category}"></td>
                                        <td>
                                            <span th:each="tech : ${#strings.arraySplit(project.technologies, ',')}"
                                                  class="badge bg-secondary me-1"
                                                  th:text="${tech.trim()}">
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge"
                                                  th:classappend="${project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).COMPLETED ? 'bg-success' :
                                                                 project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).IN_PROGRESS ? 'bg-primary' :
                                                                 project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).ON_HOLD ? 'bg-warning' : 'bg-danger'}"
                                                  th:text="${project.status.displayName}">
                                            </span>
                                        </td>
                                        <td>
                                            <div th:if="${project.startDate}" th:text="${#temporals.format(project.startDate, 'dd/MM/yyyy')}"></div>
                                            <div th:if="${project.endDate}" th:text="${#temporals.format(project.endDate, 'dd/MM/yyyy')}"></div>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a th:if="${project.githubLink}"
                                                   th:href="${project.githubLink}"
                                                   class="btn btn-sm btn-outline-dark"
                                                   target="_blank">
                                                    <i class="fab fa-github"></i>
                                                </a>
                                                <a th:if="${project.demoLink}"
                                                   th:href="${project.demoLink}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   target="_blank">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                                <form th:action="@{/admin/projects/{id}/delete(id=${project.id})}"
                                                      method="post"
                                                      style="display: inline;"
                                                      onsubmit="return confirm('Bu projeyi silmek istediğinizden emin misiniz?');">
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>