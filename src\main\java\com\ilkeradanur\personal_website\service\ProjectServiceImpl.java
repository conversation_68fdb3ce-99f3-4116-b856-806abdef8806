package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.Project;
import com.ilkeradanur.personal_website.entity.ProjectStatus;
import com.ilkeradanur.personal_website.repository.ProjectRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class ProjectServiceImpl implements ProjectService {
    private final ProjectRepository projectRepository;

    public ProjectServiceImpl(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Override
    public List<Project> getAllProjects() {
        return projectRepository.findAll();
    }

    @Override
    public Project saveProject(Project project) {
        return projectRepository.save(project);
    }

    @Override
    public void deleteProject(Long id) {
        projectRepository.deleteById(id);
    }

    @Override
    public List<Project> getProjectsByStatus(ProjectStatus status) {
        return projectRepository.findByStatus(status);
    }

    @Override
    public List<Project> getProjectsByCategory(String category) {
        return projectRepository.findByCategory(category);
    }

    @Override
    public Map<String, Long> getProjectCountByCategory() {
        return projectRepository.countProjectsByCategory().stream()
                .filter(row -> row != null && row[0] instanceof String)
                .collect(Collectors.toMap(
                    row -> (String) row[0],
                    row -> (Long) row[1]
                ));
    }

    @Override
    public List<Project> getProjectsAfterDate(LocalDate date) {
        return projectRepository.findByStartDateAfter(date);
    }

    @Override
    public List<Project> getProjectsBetweenDates(LocalDate startDate, LocalDate endDate) {
        return projectRepository.findProjectsBetweenDates(startDate, endDate);
    }

    @Override
    public List<Project> getProjectsByTechnology(String technology) {
        return projectRepository.findByTechnology(technology);
    }

    @Override
    public List<Project> getProjectsByTechnologies(Set<String> technologies) {
        return projectRepository.findAll().stream()
                .filter(p -> {
                    if (p == null || p.getTechnologies() == null || p.getTechnologies().trim().isEmpty()) return false;
                    Set<String> projectTechs = Arrays.stream(p.getTechnologies().split(","))
                            .map(String::trim)
                            .collect(Collectors.toSet());
                    return technologies.stream().allMatch(t -> projectTechs.contains(t));
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Project> getProjectsByCategoryAndTechnology(String category, String technology) {
        return projectRepository.findByCategoryAndTechnology(category, technology);
    }

    @Override
    public Map<String, Long> getProjectCountByTechnology() {
        return projectRepository.findAll().stream()
                .filter(p -> p != null && p.getTechnologies() != null)
                .flatMap(p -> Arrays.stream(p.getTechnologies().split(",")))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.groupingBy(t -> t, Collectors.counting()));
    }

    @Override
    public List<Project> getActiveProjects() {
        return projectRepository.findActiveProjects();
    }

    @Override
    public List<Project> getLatestProjects() {
        return projectRepository.findLatest5Projects();
    }

    @Override
    public Set<String> getAllTechnologies() {
        return projectRepository.findAll().stream()
                .filter(p -> p != null && p.getTechnologies() != null && !p.getTechnologies().trim().isEmpty())
                .flatMap(p -> Arrays.stream(p.getTechnologies().split(",")))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }
}