<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${title}"><PERSON>aman Çizelgesi <PERSON>ü<PERSON>le</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Zaman Çizelgesi Yönetimi</h1>
                    <a href="/admin/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> <PERSON>e <PERSON>
                    </a>
                </div>

                <!-- Bildirim<PERSON> -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Zaman Çizelgesi Ekleme Formu -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Yeni Kayıt Ekle</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/admin/timeline}" method="post" th:object="${timeline}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="type" class="form-label">Tür</label>
                                    <select class="form-select" id="type" th:field="*{type}" required>
                                        <option th:each="type : ${T(com.ilkeradanur.personalsite.entity.Timeline.TimelineType).values()}"
                                                th:value="${type}"
                                                th:text="${type.displayName}">
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="title" class="form-label">Başlık</label>
                                    <input type="text" class="form-control" id="title" th:field="*{title}" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="organization" class="form-label">Kurum/Organizasyon</label>
                                    <input type="text" class="form-control" id="organization" th:field="*{organization}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="location" class="form-label">Konum</label>
                                    <input type="text" class="form-control" id="location" th:field="*{location}">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="startDate" class="form-label">Başlangıç Tarihi</label>
                                    <input type="date" class="form-control" id="startDate" th:field="*{startDate}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="endDate" class="form-label">Bitiş Tarihi</label>
                                    <input type="date" class="form-control" id="endDate" th:field="*{endDate}">
                                    <div class="form-text">Devam ediyorsa boş bırakın</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Açıklama</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="certificateUrl" class="form-label">Sertifika/Belge URL</label>
                                <input type="url" class="form-control" id="certificateUrl" th:field="*{certificateUrl}">
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Kaydet
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Zaman Çizelgesi Listesi -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Mevcut Kayıtlar</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tür</th>
                                        <th>Başlık</th>
                                        <th>Kurum</th>
                                        <th>Tarih</th>
                                        <th>Konum</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="item : ${timelineItems}">
                                        <td>
                                            <span class="badge" 
                                                  th:classappend="${item.type == T(com.ilkeradanur.personalsite.entity.Timeline.TimelineType).EDUCATION ? 'bg-primary' : 
                                                                 item.type == T(com.ilkeradanur.personalsite.entity.Timeline.TimelineType).EXPERIENCE ? 'bg-success' : 
                                                                 item.type == T(com.ilkeradanur.personalsite.entity.Timeline.TimelineType).CERTIFICATION ? 'bg-warning' : 'bg-info'}"
                                                  th:text="${item.type.displayName}">
                                            </span>
                                        </td>
                                        <td>
                                            <strong th:text="${item.title}"></strong>
                                            <div class="small text-muted" th:text="${#strings.abbreviate(item.description, 100)}"></div>
                                        </td>
                                        <td th:text="${item.organization}"></td>
                                        <td>
                                            <div th:text="${#temporals.format(item.startDate, 'dd/MM/yyyy')}"></div>
                                            <div th:if="${item.endDate}" th:text="${#temporals.format(item.endDate, 'dd/MM/yyyy')}"></div>
                                            <div th:unless="${item.endDate}" class="text-success">Devam Ediyor</div>
                                        </td>
                                        <td th:text="${item.location}"></td>
                                        <td>
                                            <div class="btn-group">
                                                <a th:if="${item.certificateUrl}" 
                                                   th:href="${item.certificateUrl}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   target="_blank">
                                                    <i class="fas fa-certificate"></i>
                                                </a>
                                                <form th:action="@{/admin/timeline/{id}/delete(id=${item.id})}" 
                                                      method="post" 
                                                      style="display: inline;"
                                                      onsubmit="return confirm('Bu kaydı silmek istediğinizden emin misiniz?');">
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 